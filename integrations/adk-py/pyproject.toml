[project]
name = "braintrust-adk"
version = "0.1.0"
description = "Braintrust Google ADK integration"
readme = "README.md"
requires-python = ">=3.9"
dependencies = ["braintrust[otel]>=0.2.1", "google-adk>=1.9.0"]
license = { text = "Apache-2.0" }
authors = [{ name = "Braintrust", email = "<EMAIL>" }]
keywords = ["braintrust", "google-adk", "adk", "agents", "ai", "llm", "tracing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

[project.urls]
Homepage = "https://www.braintrust.dev"
Repository = "https://github.com/braintrustdata/braintrust-sdk"

[project.optional-dependencies]
dev = ["pytest>=7.0.0"]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]

[tool.uv.workspace]
members = ["examples"]
