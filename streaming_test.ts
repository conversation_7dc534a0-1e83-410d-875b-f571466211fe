import { wrap<PERSON><PERSON><PERSON><PERSON>, initLogger } from "braintrust";
import OpenA<PERSON> from "openai";

const openai = wrapOpenAI(new OpenAI());

let testnu = "06"

async function main() {
    const logger = initLogger({ projectName: "pedro-repro5019" });
    const step1 = logger.startSpan({
        name: "my root span",
        spanId: "my-root-span-custom-id" + testnu,
        event: { input: "First step" }
    });
    step1.end();
  
    const step1Export = await step1.export();
    
    // setting the span parent using the export string
    const step2 = logger.startSpan({
        name: "my child span", 
        spanId: "my-child-span-custom-id" + testnu,
        parent: step1Export,
        event: { input: "Second step" }
    });
    step2.end();
    
    // manually setting the span parents
    const step3 = logger.startSpan({
    name: "my grandchild span",
    spanId: "my-grandchild-span-custom-id" + testnu, 
    parentSpanIds: {
        spanId: "my-child-span-custom-id" + testnu,
        rootSpanId: "my-root-span-custom-id" + testnu
    },
    event: { input: "Third step" }
    });
    step3.end();

    // this span will be on the same level as the one in step 3
    const step4 = logger.startSpan({
    name: "my other grandchild span",
    spanId: "my-other-grandchild-span-custom-id" + testnu, 
    parentSpanIds: {
        spanId: "my-child-span-custom-id" + testnu,
        rootSpanId: "my-root-span-custom-id" + testnu
    },
    event: { input: "Fourth step" }
    });
    // Create a streaming chat completion
    const stream = await openai.chat.completions.create({
        messages: [
        {
            role: "system",
            content: "You are a helpful assistant that provides clear and concise answers.",
        },
        {
            role: "user",
            content: "Explain what machine learning is in 2-3 sentences.",
        },
        ],
        model: "gpt-4o-mini", // You can change this to other models like "gpt-4o", "gpt-3.5-turbo", etc.
        max_tokens: 300,
        temperature: 0.7,
        stream: true, // This enables streaming
        stream_options: {
        include_usage: true, // Include token usage information
        },
    });
    step4.log(stream)
    step4.end();

    await logger.flush();
}

main().catch(console.error);