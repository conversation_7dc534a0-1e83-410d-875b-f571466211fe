repos:
  - repo: "https://github.com/pre-commit/pre-commit-hooks"
    rev: v4.4.0
    hooks:
      - id: end-of-file-fixer
        exclude: queryscript/tests/.*\.(expected|test)$
      - id: trailing-whitespace
        exclude: queryscript/tests/.*\.(expected|test)$
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.12.8
    hooks:
      - id: ruff-check
        args: [--fix]
      - id: ruff-format
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.5
    hooks:
      - id: codespell
        exclude: >
          (?x)^(
              .*\.(json|prisma|svg)
              |.*pnpm-lock.yaml
              |.*deno.lock
              |.*.yaml
          )$
        args:
          - "-L"
          - "rouge,coo,couldn,unsecure,ontext,afterall"
  - repo: https://github.com/rbubley/mirrors-prettier
    rev: v3.3.2
    hooks:
      - id: prettier
        exclude: ^(extension/|.*\.json|.*pnpm-lock.yaml)$
